"""
Configuration management for Kritrima AI CLI.

Handles loading and managing configuration from multiple sources:
- Environment variables
- Configuration files (YAML, TOML)
- Command-line arguments
- Default values
"""

import os
import sys
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass, field
from enum import Enum

import yaml
import toml
from dotenv import load_dotenv
from pydantic import BaseModel, Field, validator


class ApprovalMode(str, Enum):
    """Approval modes for command execution."""
    SUGGEST = "suggest"      # Always ask for permission
    AUTO_EDIT = "auto-edit"  # Auto-approve file changes, ask for commands
    FULL_AUTO = "full-auto"  # Auto-approve everything (protected environment)


class ProviderType(str, Enum):
    """Supported AI providers."""
    OPENAI = "openai"
    DEEPSEEK = "deepseek"
    OLLAMA = "ollama"
    AZURE = "azure"


@dataclass
class ProviderConfig:
    """Configuration for an AI provider."""
    name: str
    base_url: str
    api_key: Optional[str] = None
    models: List[str] = field(default_factory=list)
    default_model: Optional[str] = None
    max_tokens: int = 4096
    temperature: float = 0.7
    timeout: int = 30


class Config(BaseModel):
    """Main configuration class for Kritrima AI CLI."""
    
    # Core settings
    debug: bool = Field(default=False, description="Enable debug mode")
    verbose: bool = Field(default=False, description="Enable verbose output")
    
    # AI Provider settings
    default_provider: ProviderType = Field(default=ProviderType.OPENAI)
    default_model: str = Field(default="gpt-4")
    max_context_length: int = Field(default=8000, ge=1000, le=32000)
    
    # Approval and safety settings
    approval_mode: ApprovalMode = Field(default=ApprovalMode.SUGGEST)
    safe_commands: List[str] = Field(default_factory=lambda: [
        "ls", "dir", "pwd", "echo", "cat", "head", "tail", "grep", "find",
        "git status", "git log", "git diff", "npm list", "pip list"
    ])
    dangerous_commands: List[str] = Field(default_factory=lambda: [
        "rm", "del", "format", "fdisk", "dd", "mkfs", "shutdown", "reboot"
    ])
    
    # Storage settings
    session_storage_path: Path = Field(default_factory=lambda: Path.home() / ".kritrima" / "sessions")
    cache_path: Path = Field(default_factory=lambda: Path.home() / ".kritrima" / "cache")
    log_path: Path = Field(default_factory=lambda: Path.home() / ".kritrima" / "logs")
    
    # UI settings
    use_rich_ui: bool = Field(default=True)
    show_progress: bool = Field(default=True)
    color_output: bool = Field(default=True)
    
    # Performance settings
    max_concurrent_operations: int = Field(default=5, ge=1, le=20)
    command_timeout: int = Field(default=300, ge=10, le=3600)  # 5 minutes default
    retry_attempts: int = Field(default=3, ge=1, le=10)
    
    # Provider configurations
    providers: Dict[str, ProviderConfig] = Field(default_factory=dict)
    
    class Config:
        """Pydantic configuration."""
        use_enum_values = True
        validate_assignment = True
        extra = "forbid"
    
    @validator('session_storage_path', 'cache_path', 'log_path', pre=True)
    def expand_paths(cls, v: Union[str, Path]) -> Path:
        """Expand user paths and ensure they're Path objects."""
        if isinstance(v, str):
            v = Path(v)
        return v.expanduser().resolve()
    
    def __post_init__(self) -> None:
        """Post-initialization setup."""
        # Ensure directories exist
        for path in [self.session_storage_path, self.cache_path, self.log_path]:
            path.mkdir(parents=True, exist_ok=True)
    
    @classmethod
    def load(cls, config_path: Optional[Path] = None) -> "Config":
        """
        Load configuration from multiple sources.
        
        Priority order:
        1. Command-line arguments (handled by CLI)
        2. Environment variables
        3. Configuration file
        4. Default values
        """
        # Load environment variables
        load_dotenv()
        
        # Start with default configuration
        config_data = {}
        
        # Load from configuration file
        if config_path and config_path.exists():
            config_data.update(cls._load_config_file(config_path))
        else:
            # Try default locations
            for default_path in cls._get_default_config_paths():
                if default_path.exists():
                    config_data.update(cls._load_config_file(default_path))
                    break
        
        # Override with environment variables
        config_data.update(cls._load_from_env())
        
        # Load provider configurations
        config_data["providers"] = cls._load_provider_configs()
        
        return cls(**config_data)
    
    @staticmethod
    def _get_default_config_paths() -> List[Path]:
        """Get list of default configuration file paths."""
        return [
            Path.cwd() / "kritrima.yaml",
            Path.cwd() / "kritrima.yml", 
            Path.cwd() / "config" / "kritrima.yaml",
            Path.home() / ".kritrima" / "config.yaml",
            Path.home() / ".config" / "kritrima" / "config.yaml",
        ]
    
    @staticmethod
    def _load_config_file(config_path: Path) -> Dict[str, Any]:
        """Load configuration from a YAML or TOML file."""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                if config_path.suffix.lower() in ['.yaml', '.yml']:
                    return yaml.safe_load(f) or {}
                elif config_path.suffix.lower() == '.toml':
                    return toml.load(f)
                else:
                    raise ValueError(f"Unsupported config file format: {config_path.suffix}")
        except Exception as e:
            print(f"Warning: Failed to load config file {config_path}: {e}", file=sys.stderr)
            return {}
    
    @staticmethod
    def _load_from_env() -> Dict[str, Any]:
        """Load configuration from environment variables."""
        env_mapping = {
            "KRITRIMA_DEBUG": ("debug", bool),
            "KRITRIMA_VERBOSE": ("verbose", bool),
            "DEFAULT_PROVIDER": ("default_provider", str),
            "DEFAULT_MODEL": ("default_model", str),
            "APPROVAL_MODE": ("approval_mode", str),
            "MAX_CONTEXT_LENGTH": ("max_context_length", int),
            "SESSION_STORAGE_PATH": ("session_storage_path", str),
            "CACHE_PATH": ("cache_path", str),
            "LOG_PATH": ("log_path", str),
            "USE_RICH_UI": ("use_rich_ui", bool),
            "SHOW_PROGRESS": ("show_progress", bool),
            "COLOR_OUTPUT": ("color_output", bool),
            "MAX_CONCURRENT_OPERATIONS": ("max_concurrent_operations", int),
            "COMMAND_TIMEOUT": ("command_timeout", int),
            "RETRY_ATTEMPTS": ("retry_attempts", int),
        }
        
        config_data = {}
        for env_var, (config_key, config_type) in env_mapping.items():
            value = os.getenv(env_var)
            if value is not None:
                try:
                    if config_type == bool:
                        config_data[config_key] = value.lower() in ('true', '1', 'yes', 'on')
                    elif config_type == int:
                        config_data[config_key] = int(value)
                    else:
                        config_data[config_key] = value
                except (ValueError, TypeError) as e:
                    print(f"Warning: Invalid value for {env_var}: {value} ({e})", file=sys.stderr)
        
        return config_data
    
    @staticmethod
    def _load_provider_configs() -> Dict[str, ProviderConfig]:
        """Load AI provider configurations."""
        providers = {}
        
        # OpenAI
        openai_key = os.getenv("OPENAI_API_KEY")
        if openai_key:
            providers["openai"] = ProviderConfig(
                name="openai",
                base_url="https://api.openai.com/v1",
                api_key=openai_key,
                models=["gpt-4", "gpt-4-turbo", "gpt-3.5-turbo"],
                default_model="gpt-4"
            )
        
        # Deepseek
        deepseek_key = os.getenv("DEEPSEEK_API_KEY")
        if deepseek_key:
            providers["deepseek"] = ProviderConfig(
                name="deepseek",
                base_url="https://api.deepseek.com/v1",
                api_key=deepseek_key,
                models=["deepseek-chat", "deepseek-coder"],
                default_model="deepseek-chat"
            )
        
        # Ollama (local)
        ollama_url = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434/v1")
        providers["ollama"] = ProviderConfig(
            name="ollama",
            base_url=ollama_url,
            models=["llama2", "codellama", "mistral"],
            default_model="llama2"
        )
        
        # Azure OpenAI
        azure_key = os.getenv("AZURE_OPENAI_API_KEY")
        azure_endpoint = os.getenv("AZURE_OPENAI_ENDPOINT")
        if azure_key and azure_endpoint:
            providers["azure"] = ProviderConfig(
                name="azure",
                base_url=f"{azure_endpoint}/openai/deployments",
                api_key=azure_key,
                models=["gpt-4", "gpt-35-turbo"],
                default_model="gpt-4"
            )
        
        return providers
    
    def get_provider_config(self, provider_name: Optional[str] = None) -> Optional[ProviderConfig]:
        """Get configuration for a specific provider."""
        provider_name = provider_name or self.default_provider
        return self.providers.get(provider_name)
    
    def save(self, config_path: Optional[Path] = None) -> None:
        """Save current configuration to file."""
        if not config_path:
            config_path = Path.home() / ".kritrima" / "config.yaml"
        
        config_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Convert to dict and remove non-serializable items
        config_dict = self.dict()
        
        # Convert Path objects to strings
        for key in ["session_storage_path", "cache_path", "log_path"]:
            if key in config_dict:
                config_dict[key] = str(config_dict[key])
        
        # Convert provider configs
        if "providers" in config_dict:
            providers_dict = {}
            for name, provider in config_dict["providers"].items():
                if isinstance(provider, ProviderConfig):
                    providers_dict[name] = {
                        "name": provider.name,
                        "base_url": provider.base_url,
                        "api_key": provider.api_key,
                        "models": provider.models,
                        "default_model": provider.default_model,
                        "max_tokens": provider.max_tokens,
                        "temperature": provider.temperature,
                        "timeout": provider.timeout,
                    }
                else:
                    providers_dict[name] = provider
            config_dict["providers"] = providers_dict
        
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config_dict, f, default_flow_style=False, sort_keys=True)


# Global configuration instance
_config: Optional[Config] = None


def get_config() -> Config:
    """Get the global configuration instance."""
    global _config
    if _config is None:
        _config = Config.load()
    return _config


def set_config(config: Config) -> None:
    """Set the global configuration instance."""
    global _config
    _config = config
