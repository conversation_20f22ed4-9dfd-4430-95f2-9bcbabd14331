[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
name = "kritrima-ai-cli"
version = "1.0.0"
description = "Production-ready agentic AI-powered CLI tool system for local development environments"
authors = ["Kritrima AI <<EMAIL>>"]
license = "MIT"
readme = "README.md"
homepage = "https://github.com/kritrima/ai-cli"
repository = "https://github.com/kritrima/ai-cli"
documentation = "https://docs.kritrima.ai/cli"
keywords = ["ai", "cli", "agent", "automation", "development"]
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Environment :: Console",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Tools",
    "Topic :: System :: Shells",
    "Topic :: Utilities",
]
packages = [{include = "kritrima", from = "src"}]

[tool.poetry.dependencies]
python = "^3.11"
click = "^8.1.7"
rich = "^13.7.1"
openai = "^1.51.2"
pydantic = "^2.9.2"
aiohttp = "^3.10.10"
asyncio = "^3.4.3"
pathlib = "^1.0.1"
typing-extensions = "^4.12.2"
python-dotenv = "^1.0.1"
psutil = "^6.1.0"
watchdog = "^5.0.3"
gitpython = "^3.1.43"
jsonschema = "^4.23.0"
pyyaml = "^6.0.2"
toml = "^0.10.2"
colorama = "^0.4.6"
prompt-toolkit = "^3.0.48"
pygments = "^2.18.0"

[tool.poetry.group.dev.dependencies]
pytest = "^8.3.3"
pytest-asyncio = "^0.24.0"
pytest-cov = "^6.0.0"
black = "^24.10.0"
isort = "^5.13.2"
flake8 = "^7.1.1"
mypy = "^1.13.0"
pre-commit = "^4.0.1"

[tool.poetry.scripts]
kritrima = "kritrima.cli:main"
kai = "kritrima.cli:main"

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["kritrima"]

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["src"]
omit = ["*/tests/*", "*/test_*"]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
