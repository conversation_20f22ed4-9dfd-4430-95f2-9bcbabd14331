"""
Session and context management for Kritrima AI CLI.

Handles persistent conversation state, context awareness, and session storage.
"""

import json
import uuid
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass, field, asdict
from enum import Enum

from pydantic import BaseModel, Field

from .config import get_config


class MessageRole(str, Enum):
    """Message roles in conversation."""
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"
    TOOL = "tool"


@dataclass
class Message:
    """A single message in the conversation."""
    role: MessageRole
    content: str
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    metadata: Dict[str, Any] = field(default_factory=dict)
    tool_calls: Optional[List[Dict[str, Any]]] = None
    tool_call_id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert message to dictionary."""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        data['role'] = self.role.value
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Message":
        """Create message from dictionary."""
        data = data.copy()
        data['timestamp'] = datetime.fromisoformat(data['timestamp'])
        data['role'] = MessageRole(data['role'])
        return cls(**data)


@dataclass
class ContextInfo:
    """Context information about the current environment."""
    working_directory: Path
    project_type: Optional[str] = None
    git_repository: Optional[str] = None
    git_branch: Optional[str] = None
    python_version: Optional[str] = None
    node_version: Optional[str] = None
    package_files: List[str] = field(default_factory=list)
    recent_files: List[str] = field(default_factory=list)
    environment_variables: Dict[str, str] = field(default_factory=dict)
    last_updated: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert context to dictionary."""
        data = asdict(self)
        data['working_directory'] = str(self.working_directory)
        data['last_updated'] = self.last_updated.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "ContextInfo":
        """Create context from dictionary."""
        data = data.copy()
        data['working_directory'] = Path(data['working_directory'])
        data['last_updated'] = datetime.fromisoformat(data['last_updated'])
        return cls(**data)


class Session:
    """
    Manages conversation sessions with persistent storage and context awareness.
    """
    
    def __init__(
        self,
        session_id: Optional[str] = None,
        working_directory: Optional[Path] = None,
        auto_save: bool = True
    ):
        """
        Initialize a new session.
        
        Args:
            session_id: Unique session identifier. If None, generates a new one.
            working_directory: Working directory for the session. If None, uses current directory.
            auto_save: Whether to automatically save session changes.
        """
        self.session_id = session_id or str(uuid.uuid4())
        self.working_directory = working_directory or Path.cwd()
        self.auto_save = auto_save
        
        self.created_at = datetime.now(timezone.utc)
        self.last_activity = self.created_at
        self.messages: List[Message] = []
        self.context: Optional[ContextInfo] = None
        self.metadata: Dict[str, Any] = {}
        
        self.config = get_config()
        self._session_file = self._get_session_file()
        
        # Initialize context
        self._update_context()
    
    def _get_session_file(self) -> Path:
        """Get the file path for this session."""
        return self.config.session_storage_path / f"{self.session_id}.json"
    
    def add_message(
        self,
        role: MessageRole,
        content: str,
        metadata: Optional[Dict[str, Any]] = None,
        tool_calls: Optional[List[Dict[str, Any]]] = None,
        tool_call_id: Optional[str] = None
    ) -> Message:
        """
        Add a message to the conversation.
        
        Args:
            role: The role of the message sender
            content: The message content
            metadata: Optional metadata for the message
            tool_calls: Optional tool calls made by the assistant
            tool_call_id: Optional tool call ID for tool responses
            
        Returns:
            The created message
        """
        message = Message(
            role=role,
            content=content,
            metadata=metadata or {},
            tool_calls=tool_calls,
            tool_call_id=tool_call_id
        )
        
        self.messages.append(message)
        self.last_activity = datetime.now(timezone.utc)
        
        if self.auto_save:
            self.save()
        
        return message
    
    def add_user_message(self, content: str, metadata: Optional[Dict[str, Any]] = None) -> Message:
        """Add a user message to the conversation."""
        return self.add_message(MessageRole.USER, content, metadata)
    
    def add_assistant_message(
        self,
        content: str,
        metadata: Optional[Dict[str, Any]] = None,
        tool_calls: Optional[List[Dict[str, Any]]] = None
    ) -> Message:
        """Add an assistant message to the conversation."""
        return self.add_message(MessageRole.ASSISTANT, content, metadata, tool_calls)
    
    def add_system_message(self, content: str, metadata: Optional[Dict[str, Any]] = None) -> Message:
        """Add a system message to the conversation."""
        return self.add_message(MessageRole.SYSTEM, content, metadata)
    
    def add_tool_message(
        self,
        content: str,
        tool_call_id: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Message:
        """Add a tool response message to the conversation."""
        return self.add_message(MessageRole.TOOL, content, metadata, tool_call_id=tool_call_id)
    
    def get_messages(
        self,
        role: Optional[MessageRole] = None,
        limit: Optional[int] = None
    ) -> List[Message]:
        """
        Get messages from the conversation.
        
        Args:
            role: Filter by message role
            limit: Maximum number of messages to return (most recent first)
            
        Returns:
            List of messages
        """
        messages = self.messages
        
        if role:
            messages = [msg for msg in messages if msg.role == role]
        
        if limit:
            messages = messages[-limit:]
        
        return messages
    
    def get_conversation_for_api(self, max_tokens: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Get conversation formatted for API calls.
        
        Args:
            max_tokens: Maximum tokens to include (approximate)
            
        Returns:
            List of message dictionaries suitable for API calls
        """
        api_messages = []
        
        # Add system context if available
        if self.context:
            system_content = self._build_system_context()
            api_messages.append({
                "role": "system",
                "content": system_content
            })
        
        # Add conversation messages
        for message in self.messages:
            api_message = {
                "role": message.role.value,
                "content": message.content
            }
            
            # Add tool calls for assistant messages
            if message.tool_calls:
                api_message["tool_calls"] = message.tool_calls
            
            # Add tool call ID for tool messages
            if message.tool_call_id:
                api_message["tool_call_id"] = message.tool_call_id
            
            api_messages.append(api_message)
        
        # TODO: Implement token-based truncation if max_tokens is specified
        
        return api_messages
    
    def _build_system_context(self) -> str:
        """Build system context message from current environment."""
        if not self.context:
            return "You are Kritrima AI, an intelligent development assistant."
        
        context_parts = [
            "You are Kritrima AI, an intelligent development assistant.",
            f"Current working directory: {self.context.working_directory}",
        ]
        
        if self.context.project_type:
            context_parts.append(f"Project type: {self.context.project_type}")
        
        if self.context.git_repository:
            context_parts.append(f"Git repository: {self.context.git_repository}")
            if self.context.git_branch:
                context_parts.append(f"Current branch: {self.context.git_branch}")
        
        if self.context.python_version:
            context_parts.append(f"Python version: {self.context.python_version}")
        
        if self.context.node_version:
            context_parts.append(f"Node.js version: {self.context.node_version}")
        
        if self.context.package_files:
            context_parts.append(f"Package files found: {', '.join(self.context.package_files)}")
        
        if self.context.recent_files:
            context_parts.append(f"Recent files: {', '.join(self.context.recent_files[:10])}")
        
        return "\n".join(context_parts)
    
    def _update_context(self) -> None:
        """Update context information about the current environment."""
        from .storage.context_manager import ContextManager
        
        context_manager = ContextManager(self.working_directory)
        self.context = context_manager.get_context()
    
    def save(self) -> None:
        """Save session to disk."""
        session_data = {
            "session_id": self.session_id,
            "working_directory": str(self.working_directory),
            "created_at": self.created_at.isoformat(),
            "last_activity": self.last_activity.isoformat(),
            "messages": [msg.to_dict() for msg in self.messages],
            "context": self.context.to_dict() if self.context else None,
            "metadata": self.metadata,
        }
        
        self._session_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(self._session_file, 'w', encoding='utf-8') as f:
            json.dump(session_data, f, indent=2, ensure_ascii=False)
    
    @classmethod
    def load(cls, session_id: str) -> Optional["Session"]:
        """
        Load a session from disk.
        
        Args:
            session_id: The session ID to load
            
        Returns:
            The loaded session, or None if not found
        """
        config = get_config()
        session_file = config.session_storage_path / f"{session_id}.json"
        
        if not session_file.exists():
            return None
        
        try:
            with open(session_file, 'r', encoding='utf-8') as f:
                session_data = json.load(f)
            
            session = cls(
                session_id=session_data["session_id"],
                working_directory=Path(session_data["working_directory"]),
                auto_save=True
            )
            
            session.created_at = datetime.fromisoformat(session_data["created_at"])
            session.last_activity = datetime.fromisoformat(session_data["last_activity"])
            session.messages = [Message.from_dict(msg) for msg in session_data["messages"]]
            session.metadata = session_data.get("metadata", {})
            
            if session_data.get("context"):
                session.context = ContextInfo.from_dict(session_data["context"])
            
            return session
            
        except Exception as e:
            print(f"Error loading session {session_id}: {e}")
            return None
    
    @classmethod
    def list_sessions(cls) -> List[Dict[str, Any]]:
        """
        List all available sessions.
        
        Returns:
            List of session metadata
        """
        config = get_config()
        sessions = []
        
        if not config.session_storage_path.exists():
            return sessions
        
        for session_file in config.session_storage_path.glob("*.json"):
            try:
                with open(session_file, 'r', encoding='utf-8') as f:
                    session_data = json.load(f)
                
                sessions.append({
                    "session_id": session_data["session_id"],
                    "working_directory": session_data["working_directory"],
                    "created_at": session_data["created_at"],
                    "last_activity": session_data["last_activity"],
                    "message_count": len(session_data.get("messages", [])),
                })
                
            except Exception:
                continue  # Skip corrupted session files
        
        # Sort by last activity (most recent first)
        sessions.sort(key=lambda x: x["last_activity"], reverse=True)
        
        return sessions
    
    def delete(self) -> bool:
        """
        Delete this session from disk.
        
        Returns:
            True if deleted successfully, False otherwise
        """
        try:
            if self._session_file.exists():
                self._session_file.unlink()
            return True
        except Exception:
            return False
    
    def clear_messages(self) -> None:
        """Clear all messages from the session."""
        self.messages.clear()
        self.last_activity = datetime.now(timezone.utc)
        
        if self.auto_save:
            self.save()
    
    def get_summary(self) -> str:
        """Get a summary of the session."""
        message_count = len(self.messages)
        user_messages = len([m for m in self.messages if m.role == MessageRole.USER])
        assistant_messages = len([m for m in self.messages if m.role == MessageRole.ASSISTANT])
        
        return (
            f"Session {self.session_id[:8]}...\n"
            f"Working directory: {self.working_directory}\n"
            f"Created: {self.created_at.strftime('%Y-%m-%d %H:%M:%S')}\n"
            f"Last activity: {self.last_activity.strftime('%Y-%m-%d %H:%M:%S')}\n"
            f"Messages: {message_count} total ({user_messages} user, {assistant_messages} assistant)"
        )
