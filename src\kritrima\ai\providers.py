"""
Universal AI provider interface using OpenAI SDK.

Supports OpenAI, Deepseek, Ollama, Azure OpenAI, and other OpenAI-compatible APIs.
"""

import asyncio
from typing import Any, Dict, List, Optional, AsyncIterator, Union
from abc import ABC, abstractmethod

from openai import OpenAI, AsyncOpenAI
from openai.types.chat import <PERSON><PERSON><PERSON><PERSON>ple<PERSON>, ChatCompletionChunk
from openai.types.chat.chat_completion import Choice
from openai.types.chat.chat_completion_chunk import <PERSON>Delta

from ..config import Config, ProviderConfig, get_config
from .streaming import StreamingResponse


class AIProvider(ABC):
    """Abstract base class for AI providers."""
    
    def __init__(self, config: ProviderConfig):
        """Initialize the provider with configuration."""
        self.config = config
        self.client = self._create_client()
        self.async_client = self._create_async_client()
    
    def _create_client(self) -> OpenAI:
        """Create synchronous OpenAI client."""
        return OpenAI(
            api_key=self.config.api_key or "dummy-key",
            base_url=self.config.base_url,
            timeout=self.config.timeout,
        )
    
    def _create_async_client(self) -> AsyncOpenAI:
        """Create asynchronous OpenAI client."""
        return AsyncOpenAI(
            api_key=self.config.api_key or "dummy-key", 
            base_url=self.config.base_url,
            timeout=self.config.timeout,
        )
    
    async def chat_completion(
        self,
        messages: List[Dict[str, Any]],
        model: Optional[str] = None,
        tools: Optional[List[Dict[str, Any]]] = None,
        tool_choice: Optional[Union[str, Dict[str, Any]]] = None,
        stream: bool = False,
        **kwargs
    ) -> Union[ChatCompletion, AsyncIterator[ChatCompletionChunk]]:
        """
        Create a chat completion.
        
        Args:
            messages: List of conversation messages
            model: Model to use (defaults to provider's default)
            tools: Available tools for function calling
            tool_choice: Tool choice strategy
            stream: Whether to stream the response
            **kwargs: Additional parameters
            
        Returns:
            Chat completion response or async iterator for streaming
        """
        model = model or self.config.default_model
        
        # Prepare request parameters
        params = {
            "model": model,
            "messages": messages,
            "max_tokens": kwargs.get("max_tokens", self.config.max_tokens),
            "temperature": kwargs.get("temperature", self.config.temperature),
            "stream": stream,
        }
        
        # Add tools if provided
        if tools:
            params["tools"] = tools
            if tool_choice:
                params["tool_choice"] = tool_choice
        
        # Add any additional parameters
        for key, value in kwargs.items():
            if key not in params and value is not None:
                params[key] = value
        
        try:
            if stream:
                return await self.async_client.chat.completions.create(**params)
            else:
                return await self.async_client.chat.completions.create(**params)
        except Exception as e:
            raise AIProviderError(f"Chat completion failed: {e}") from e
    
    async def stream_chat_completion(
        self,
        messages: List[Dict[str, Any]],
        model: Optional[str] = None,
        tools: Optional[List[Dict[str, Any]]] = None,
        tool_choice: Optional[Union[str, Dict[str, Any]]] = None,
        **kwargs
    ) -> StreamingResponse:
        """
        Create a streaming chat completion.
        
        Args:
            messages: List of conversation messages
            model: Model to use
            tools: Available tools
            tool_choice: Tool choice strategy
            **kwargs: Additional parameters
            
        Returns:
            StreamingResponse object
        """
        stream = await self.chat_completion(
            messages=messages,
            model=model,
            tools=tools,
            tool_choice=tool_choice,
            stream=True,
            **kwargs
        )
        
        return StreamingResponse(stream)
    
    def get_available_models(self) -> List[str]:
        """Get list of available models for this provider."""
        return self.config.models
    
    def validate_model(self, model: str) -> bool:
        """Check if a model is available for this provider."""
        return model in self.config.models
    
    @property
    def name(self) -> str:
        """Get provider name."""
        return self.config.name


class OpenAIProvider(AIProvider):
    """OpenAI provider implementation."""
    
    def __init__(self, config: ProviderConfig):
        """Initialize OpenAI provider."""
        if not config.api_key:
            raise AIProviderError("OpenAI API key is required")
        super().__init__(config)


class DeepseekProvider(AIProvider):
    """Deepseek provider implementation."""
    
    def __init__(self, config: ProviderConfig):
        """Initialize Deepseek provider."""
        if not config.api_key:
            raise AIProviderError("Deepseek API key is required")
        super().__init__(config)


class OllamaProvider(AIProvider):
    """Ollama provider implementation."""
    
    def __init__(self, config: ProviderConfig):
        """Initialize Ollama provider."""
        # Ollama doesn't require an API key
        super().__init__(config)
    
    async def get_available_models(self) -> List[str]:
        """Get available models from Ollama server."""
        try:
            # Try to get models from Ollama API
            response = await self.async_client.models.list()
            return [model.id for model in response.data]
        except Exception:
            # Fall back to configured models
            return self.config.models


class AzureOpenAIProvider(AIProvider):
    """Azure OpenAI provider implementation."""
    
    def __init__(self, config: ProviderConfig):
        """Initialize Azure OpenAI provider."""
        if not config.api_key:
            raise AIProviderError("Azure OpenAI API key is required")
        super().__init__(config)
    
    def _create_client(self) -> OpenAI:
        """Create Azure OpenAI client."""
        return OpenAI(
            api_key=self.config.api_key,
            base_url=self.config.base_url,
            timeout=self.config.timeout,
            default_headers={"api-version": "2024-02-15-preview"}
        )
    
    def _create_async_client(self) -> AsyncOpenAI:
        """Create async Azure OpenAI client."""
        return AsyncOpenAI(
            api_key=self.config.api_key,
            base_url=self.config.base_url,
            timeout=self.config.timeout,
            default_headers={"api-version": "2024-02-15-preview"}
        )


class AIProviderError(Exception):
    """Exception raised by AI providers."""
    pass


# Provider registry
_PROVIDERS = {
    "openai": OpenAIProvider,
    "deepseek": DeepseekProvider,
    "ollama": OllamaProvider,
    "azure": AzureOpenAIProvider,
}


def get_provider(provider_name: Optional[str] = None) -> AIProvider:
    """
    Get an AI provider instance.
    
    Args:
        provider_name: Name of the provider to get. If None, uses default.
        
    Returns:
        AIProvider instance
        
    Raises:
        AIProviderError: If provider is not available or configured
    """
    config = get_config()
    
    # Use default provider if none specified
    if not provider_name:
        provider_name = config.default_provider
    
    # Get provider configuration
    provider_config = config.get_provider_config(provider_name)
    if not provider_config:
        raise AIProviderError(f"Provider '{provider_name}' is not configured")
    
    # Get provider class
    provider_class = _PROVIDERS.get(provider_name)
    if not provider_class:
        raise AIProviderError(f"Provider '{provider_name}' is not supported")
    
    # Create and return provider instance
    try:
        return provider_class(provider_config)
    except Exception as e:
        raise AIProviderError(f"Failed to create provider '{provider_name}': {e}") from e


def list_available_providers() -> List[str]:
    """Get list of available provider names."""
    config = get_config()
    return list(config.providers.keys())


def list_supported_providers() -> List[str]:
    """Get list of supported provider types."""
    return list(_PROVIDERS.keys())
