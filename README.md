# 🤖 Kritrima AI CLI - Agentic AI-Powered Development Assistant

[![Python 3.11+](https://img.shields.io/badge/python-3.11+-blue.svg)](https://www.python.org/downloads/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Code style: black](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)

A production-ready, agentic AI-powered CLI tool system that lives and breathes in your local development environment. Built with modern architecture, advanced features, and enterprise-grade quality for Windows 11 WSL, macOS, and Linux.

## 🚀 Features

### 🧠 **Intelligent AI Integration**
- **Universal AI Provider Support**: OpenAI, Deepseek, Ollama, Azure OpenAI
- **Real-time Streaming**: Live AI responses with typing indicators
- **Function Tool Calling**: Advanced schema-based tool execution
- **Context-Aware Conversations**: Persistent memory across sessions

### 🛠️ **Comprehensive Development Tools**
- **Shell Command Execution**: Safe, monitored command execution
- **File Operations**: Read, write, search, grep, glob, create, delete, move, copy
- **Project Discovery**: Automatic indexing and context awareness
- **Git Integration**: Repository-aware operations

### 🔒 **Enterprise-Grade Safety**
- **Approval System**: Suggest, Auto-Edit, and Full-Auto modes
- **Command Validation**: Security checks and risk assessment
- **Error Handling**: Robust error recovery and retry logic
- **Session Management**: Persistent context and conversation history

### 🎨 **Modern User Experience**
- **Rich Terminal UI**: Beautiful, interactive command-line interface
- **Progress Indicators**: Real-time status updates and progress bars
- **Syntax Highlighting**: Code and output highlighting
- **Cross-Platform**: Windows 11 WSL, macOS, and Linux support

## 📦 Installation

### Using Poetry (Recommended)
```bash
# Clone the repository
git clone https://github.com/kritrima/ai-cli.git
cd ai-cli

# Install with Poetry
poetry install

# Activate the environment
poetry shell
```

### Using pip
```bash
# Clone the repository
git clone https://github.com/kritrima/ai-cli.git
cd ai-cli

# Install dependencies
pip install -r requirements.txt

# Install in development mode
pip install -e .
```

## ⚙️ Configuration

### Environment Setup
Create a `.env` file in your project root:

```env
# AI Provider Configuration
OPENAI_API_KEY=your_openai_api_key_here
DEEPSEEK_API_KEY=your_deepseek_api_key_here

# Provider Settings
DEFAULT_PROVIDER=openai
DEFAULT_MODEL=gpt-4

# CLI Settings
APPROVAL_MODE=suggest  # suggest, auto-edit, full-auto
MAX_CONTEXT_LENGTH=8000
SESSION_STORAGE_PATH=~/.kritrima/sessions
```

### Provider Configuration
The system supports multiple AI providers through the OpenAI SDK:

```yaml
# config/providers.yaml
providers:
  openai:
    base_url: "https://api.openai.com/v1"
    models: ["gpt-4", "gpt-3.5-turbo"]
  
  deepseek:
    base_url: "https://api.deepseek.com/v1"
    models: ["deepseek-chat", "deepseek-coder"]
  
  ollama:
    base_url: "http://localhost:11434/v1"
    models: ["llama2", "codellama"]
```

## 🎯 Usage

### Basic Commands
```bash
# Start an interactive session
kritrima chat

# Execute a single command
kritrima "Create a React component for a todo list"

# Use the short alias
kai "Fix the build errors in this project"

# Show help
kritrima --help
```

### Advanced Usage
```bash
# Set approval mode
kritrima --mode auto-edit "Refactor this code for better performance"

# Use specific AI provider
kritrima --provider deepseek "Analyze this codebase"

# Load previous session
kritrima --session last "Continue where we left off"

# Enable debug mode
kritrima --debug "Debug this failing test"
```

## 🏗️ Architecture

The system follows a modular, enterprise-grade architecture:

```
src/kritrima/
├── cli.py                 # Main CLI entry point
├── agent_engine.py        # Central orchestrator
├── config.py             # Configuration management
├── session.py            # Session and context management
├── ai/
│   ├── providers.py      # Universal LLM provider interface
│   ├── streaming.py      # Real-time streaming responses
│   └── tools.py          # Function tool calling schema
├── execution/
│   ├── shell_executor.py # Safe shell command execution
│   ├── file_operations.py# Comprehensive file operations
│   ├── approval_system.py# Safety and approval mechanisms
│   └── output_formatter.py# Result formatting
├── safety/
│   ├── error_handler.py  # Robust error handling
│   ├── retry_logic.py    # Intelligent retry mechanisms
│   └── security.py       # Security validations
├── storage/
│   ├── context_manager.py# Project structure awareness
│   ├── session_storage.py# Persistent session memory
│   └── history.py        # Conversation history
└── ui/
    ├── terminal_ui.py    # Rich terminal interface
    └── progress.py       # Progress indicators
```

## 🔧 Development

### Setting up Development Environment
```bash
# Install development dependencies
poetry install --with dev

# Install pre-commit hooks
pre-commit install

# Run tests
pytest

# Run with coverage
pytest --cov=src/kritrima

# Format code
black src/ tests/
isort src/ tests/

# Type checking
mypy src/
```

### Running Tests
```bash
# Run all tests
pytest

# Run specific test categories
pytest -m unit
pytest -m integration
pytest -m "not slow"

# Run with verbose output
pytest -v

# Run with coverage report
pytest --cov=src/kritrima --cov-report=html
```

## 📚 Documentation

- **[User Guide](docs/user-guide.md)**: Complete usage documentation
- **[API Reference](docs/api-reference.md)**: Detailed API documentation
- **[Architecture Guide](docs/architecture.md)**: System design and architecture
- **[Contributing](docs/contributing.md)**: Development and contribution guidelines

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](docs/contributing.md) for details.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Built with the OpenAI SDK for universal AI provider compatibility
- Powered by modern Python tooling and best practices
- Inspired by the need for intelligent, safe AI-powered development tools

---

**Kritrima AI CLI** - Where AI meets development productivity. 🚀
